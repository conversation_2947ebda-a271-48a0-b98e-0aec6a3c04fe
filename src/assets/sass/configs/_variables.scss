/* Set Variable to :root DOM */
:root {

    // Typo colors
    --color-text:           #{$paragraph};
    --color-title:          var(--dark-color);
    --color-btn:            #{$white};
    --color-btn-hover:      #{$white};

    // Background colors
    --color-bg:             #{$background};
    --color-bg-light:       #{$background-light};
    --color-bg-dark:       #{$dark};

    --color-border: #{$border-color};
    // Line heights 
    --line-height-100: 100%;
    --line-height-110: 110%;
    --line-height-120: 120%;
    --line-height-140: 140%;
    --line-height-150: 150%;
    --line-height-160: 160%;

    // Letter spacing (n = negative, p = positive)
    --letter-spacing-n064: -0.64px;
    --letter-spacing-n1: -1px;
    --letter-spacing-n2: -2px;
    --letter-spacing-p3: 3px;



    // Set main colors
    // --primary-color:        #{$primary-color};
    // --primary-color-darker: #{$primary-color-darker};
    // --secondary-color:      #{$secondary-color};
    // --complementary-color:  #{$complementary-color};
    // --dark-color:           #{$dark};

    // // Typo colors
    // --color-text:           #{$paragraph};
    // --color-title:          var(--dark-color);
    // --color-bg:             #{$background};
    // --color-bg-default:     #{$white};
    // --color-bg-light:       #{$background-light};

    // // Font family
    // --font-title :      "Poppins", sans-serif;
    // --font-primary :    "Manrope", sans-serif;
    // --font-secondary:   var(--font-title);
    // --font-special:     "Open Sans", sans-serif;


    // // // // //
    // HEADINGS // 
    // // // // //

    // --font-size-h1-large:   #{responsive-size(80px, 100px, $desktop-lg)};
    // --line-height-h1-large: #{responsive-size(70px, 90px, $desktop-lg)};
    // --font-size-h1:         #{responsive-size(62px, 72px, $desktop-lg)};
    // --line-height-h1:       #{responsive-size(60px, 65px, $desktop-lg)};
    // --font-size-h1-small:   #{responsive-size(40px, 52px, $desktop-lg)};
    // --line-height-h1-small: #{responsive-size(42px, 55px, $desktop-lg)};

    // --font-size-h2-large:   #{responsive-size(42px, 56px, $desktop-lg)};
    // --line-height-h2-large: #{responsive-size(48px, 60px, $desktop-lg)};
    // --font-size-h2:         #{responsive-size(36px, 46px, $desktop-lg)};
    // --line-height-h2:       #{responsive-size(36px, 50px, $desktop-lg)};
    // --font-size-h2-small:   #{responsive-size(32px, 42px, $desktop-lg)};
    // --line-height-h2-small: #{responsive-size(36px, 50px, $desktop-lg)};

    // --font-size-h3:         #{responsive-size(28px, 36px, $desktop-lg)};
    // --line-height-h3:       #{responsive-size(30px, 38px, $desktop-lg)};
    // --font-size-h3-small:   #{responsive-size(24px, 28px, $desktop-lg)}; 
    // --line-height-h3-small: #{responsive-size(34px, 38px, $desktop-lg)};

    // --font-size-h4:         #{responsive-size(22px, 24px, $desktop-lg)};
    // --line-height-h4:       #{responsive-size(26px, 28px, $desktop-lg)};

    // --font-size-h5:         #{responsive-size(20px, 20px, $desktop-lg)};
    // --line-height-h5:       #{responsive-size(20px, 20px, $desktop-lg)};

    // --font-size-h6:         #{responsive-size(20px, 20px, $desktop-lg)};
    // --line-height-h6:       #{responsive-size(20px, 20px, $desktop-lg)};



    // // // // // //
    //  PARAGRAPHS // 
    // // // // // //

    --font-size-large:          #{responsive-size(20px, 22px, $desktop-lg)};
    --line-height-large:        #{responsive-size(32px, 32px, $desktop-lg)};

    // --font-size-medium:         #{responsive-size(16px, 18px, $desktop-lg)};
    // --line-height-medium:       #{responsive-size(26px, 28px, $desktop-lg)};
    
    // --font-size-small:          #{responsive-size(14px, 16px, $desktop-lg)};
    // --line-height-small:        #{responsive-size(22px, 24px, $desktop-lg)};
    
    // --font--size-description:   #{responsive-size(13px, 13px, $desktop-lg)};
    // --line-height-description:  #{responsive-size(16px, 16px, $desktop-lg)};

    // --font-size-testimony:      #{responsive-size(22px, 26px, $desktop-lg)};
    // --line-height-testimony:    #{responsive-size(28px, 36px, $desktop-lg)};

    // --font-size-blog:           #{responsive-size(22px, 24px, $desktop-lg)};
    // --line-height-blog:         #{responsive-size(26px, 28px, $desktop-lg)};



    // // // // //
    // SPECIALS // 
    // // // // //

    // Prices
    // --font-size-price-big:      #{responsive-size(20px, 22px, $desktop-lg)};
    // --line-height-price-big:    #{responsive-size(24px, 26px, $desktop-lg)};
    // --font-size-price-small:    #{responsive-size(14px, 16px, $desktop-lg)};
    // --line-height-price-small:  #{responsive-size(16px, 18px, $desktop-lg)};

    // CTA
    // --font-size-cta-large:      #{responsive-size(16px, 18px, $desktop-lg)};
    // --line-height-cta-large:    #{responsive-size(20px, 22px, $desktop-lg)};
    // --font-size-cta-small:      #{responsive-size(14px, 14px, $desktop-lg)};
    // --line-height-cta-small:    #{responsive-size(18px, 18px, $desktop-lg)};

    // Surtitles
    // --font-size-eyebrow-large:      #{responsive-size(15px, 18px, $desktop-lg)};
    // --line-height-eyebrow-large:    #{responsive-size(20px, 22px, $desktop-lg)};
    // --font-size-eyebrow:            #{responsive-size(14px, 16px, $desktop-lg)};
    // --line-height-eyebrow:          #{responsive-size(16px, 18px, $desktop-lg)};

    // Thumbnails
    // --font-size-thumbnail-title:        #{responsive-size(20px, 24px, $desktop-lg)};
    // --line-height-thumbnail-title:      #{responsive-size(28px, 32px, $desktop-lg)};
    // --font-size-thumbnail-meta:         #{responsive-size(13px, 14px, $desktop-lg)};
    // --line-height-thumbnail-meta:       #{responsive-size(15px, 18px, $desktop-lg)};
    // --font-size-thumbnail-description:  #{responsive-size(12px, 12px, $desktop-lg)};
    // --line-height-thumbnail-description:#{responsive-size(16px, 16px, $desktop-lg)};

    // // // // 
    //  NAV  // 
    // // // //
    // --text-nav-header-primary-size: #{rs(18px, 20px, $desktop-lg)};
    // --text-nav-header-primary-weight: 600;
    // --text-nav-header-secondary-size: #{rs(16px, 18px, $desktop-lg)};
    // --text-nav-header-secondary-weight: 600;

    // --color-nav-primary-text: #151515;


    // // // // 
    // GRID  // 
    // // // //

    // Spacing (todo : validé ceux des maquettes)
    // --spacing-small:    #{rem(20px)};
    // --spacing-normal:   #{rem(50px)};
    // --spacing-medium:   #{rem(80px)};
    // --spacing-large:    #{rem(120px)};
    


    // Metrics
    --unit: #{$column-gap};
    --container-margin: #{rem(150px)};

    // Grid
    --grid-columns: #{$base-column-nb};
    --grid-gutter:  var(--unit); // $column-gap in config.scss
    --grid-margin:  var(--container-margin);

    // Set a max-width for the container (useful for large screens)
    @media screen and (min-width: $desktop-xxlg) {
        --container-margin: 10vw;
    }

    // Small desktop
    @media (max-width: $desktop) {
        --container-margin: #{rem(60px)};
    }

    // Tablet and mobile
    @media screen and (max-width: $tablet) {
        // Metric
        --unit: 20px;
        --container-margin: 20px;

        // Grid
        --grid-columns: 4;

        // Spacing
        --spacing-normal:   #{rem(30px)};
        --spacing-medium:   #{rem(60px)};
        --spacing-large:    #{rem(100px)};
    }
}
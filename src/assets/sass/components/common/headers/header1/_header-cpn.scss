#header {
	.inner {
		padding: rem(24px) rem(40px);
		z-index: 9999;
		background: var(--color-bg-default);

		@media (max-width: $tablet-lg) {
			padding: rem(24px) rem(20px);
		}
	}

	a {
		cursor: pointer;
		color: var(--color-nav-primary-text);
		line-height: 1;
	}
	
	// Submenu 
	.sub-header {

		.sub-head-menu {
			display: flex;
			justify-content: flex-end;
			padding-bottom: rem(25px);
			gap: 32px;

			.language a, .number-ctn a {
				font-size: var(--text-nav-header-primary-size);
				font-weight: var(--text-nav-header-primary-weight);
			}

			.number-ctn {
				a {
					i {
						padding-right: rem(10px);
						font-size: rem(16px);
					}
				}
			}
		}
	}

	// Main
	.main-header {
		display: flex;
		align-items: center;
		justify-content: space-between;

		// Logo
		.logo-ctn {
			a {

			}
		}

		// Menu
		.main-menu-ctn {
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 32px;

			.main-menu {
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 32px;

				.item-menu {
					position: relative;
					height: 60px;

					// Main menu links
					> a {
						font-size:  var(--text-nav-header-primary-size);
						font-weight: var(--text-nav-header-primary-weight);
						display: flex;
						align-items: center;
						line-height: normal;
						height: 60px;

						// chevron
						i {
							padding-left: rem(8px);
							font-size: rem(10px);
						}

						&:hover {
							opacity: 0.5;
						}
					}
					
					&:hover, ul:hover {
						> ul {
							opacity: 1;
							z-index: 200;
							pointer-events: auto;
							transform: translateY(0);
						}
					}

					// Submenu panel
					> ul {
						position: absolute;
						padding: rem(32px) rem(24px);
						background: var(--color-bg-section-2, #C1D0E2);
						border-radius: 16px;
						z-index: 99;
						top: 60px;
						transition: $transition;
						transform: translateY(-20px);
						
						z-index: -1;
						opacity: 0;
						pointer-events: none;


						&:before {
							content: "";
							width: rem(15px);
							height: rem(15px);
							background: var(--color-bg-section-2, #C1D0E2);
						}

						li {
							a {
								font-size:  var(--text-nav-header-secondary-size);
								font-weight: var(--text-nav-header-secondary-weight);
								line-height: normal;
								padding-bottom: 10px;
								white-space: nowrap;
							}

							&:last-child a {
								padding-bottom: 0;
							}

							&:hover a {
								opacity: 0.5;
							}
						}
					}
				}
			}

		}
	}

	// Classic Header
	.-classic {
		// height: rem(160px);

		@media (max-width: $desktop-sm) {
			.main-menu  {
				gap: 15px;
			}

			.remax-logo {
				display: none;
			}
		}

		@media (max-width: $tablet-lg) {
			.main-menu-ctn  {
				justify-content: flex-end;
				width: 100%;
				padding-right: 20px;

				.main-menu {
					display: none;
				}

				.remax-logo img {
					width: 40px;
				}
			}
			
		}

		// mobile menu toggle
		.header-menu-toggle {
			@media (min-width: ($tablet-lg + 1px)) {
				display: none;
			}
		}
	}

	.-inline {
		.main-menu-ctn {
			width: 100%;
			padding-left: 64px;

			.secondary-menu {
				gap: 20px;
			}
		}

		@media (max-width: $desktop) {
			.main-menu-ctn  {
				padding-left: 30px;
			}

			.secondary-menu {
				gap: 20px;
			}
		}

		@media (max-width: $desktop-sm) {
			.main-menu-ctn  {
				justify-content: flex-end;
				padding-right: 20px;

				.main-menu {
					display: none !important;
				}
			}

			.header-menu-toggle {
				display: inline-block;
			}
			
		}

		// mobile menu toggle
		.header-menu-toggle {
			@media (min-width: ($desktop-sm + 1px)) {
				display: none;
			}
		}

		@media (max-width: $tablet) {
			.secondary-menu {
				display: none;
			}
		}
	}

	.-center {

		.main-header {
			justify-content: center;
		}

		ul {
			li, a {
				text-align: start !important;
			}
		}

		.sub-header {
			display: flex;
			justify-content: center;
			position: relative;
			height: 80px;

			.sub-head-menu {
				position: absolute;
				right: 0;
			}

			.sub-head-menu {
				align-items: center;
			}
		}

		@media (max-width: $tablet-lg) {
			.main-header {
				display: none;
			}

			.sub-header {
				height: unset;
				justify-content: space-between;

				.sub-head-menu {
					display: none;
				}
			}
		}
	}
}


























// // /*
// // 	Sub Header Component
// // */
// // $subHeaderHeight1: 45px;
// // $subHeaderBG1: white;
// // $subHeaderLinkSize1: 13px;
// // $subHeaderLinkColor1: var(--color-text);

// // $subHeaderFavoriteBorderLeft1: 1px solid #D8D8D8;
// // $subHeaderFavoritePadding1:0 20px;
// // $subHeaderFavoriteSize1: 14px;

// // $subHeaderNumberBG1: var(--primary-color);
// // $subHeaderNumberBGHover1: var(--primary-color-darker);;
// // $subHeaderNumberBorderLeft1: 1px solid #D8D8D8;
// // $subHeaderNumberPadding1: 0 17px;
// // $subHeaderNumberColor1: white;
// // $subHeaderNumberSize1: 18px;
// // $subHeaderNumberWeight1: 900;
// // $subHeaderNumberOpacity1: 0.96;
// // $subHeaderNumberPhoneBorder1: none;
// // $subHeaderNumberPhoneRadius1: 0px;

// // /*
// // 	Header Component
// // */
// // $headerHeight1: 100px;
// // $headerBG1: $white;
// // $headerBorderBottom1: 1px solid #DDDDDD;
// // $headerLinkPaddingRight1: 35px;
// // $headerLinkSize1: 14px;
// // $headerLinkWeight1: 700;
// // $headerLinkLineHeight1: 17px;
// // $headerLinkColor1: $black;
// // $headerLinkTransform1: uppercase;
// // $headerSubMenuBG1: #F1ECE1;
// // $headerSubMenuLinkBGHover1: transparent;
// // $headerSubMenuLinkColor1: var(--color-text);
// // $headerSubMenuLinkColorHover1: var(--color-text);
// // $headerSubMenuLinkLineHeight1: 20px;
// // $headerSubMenuLinkSize1: 14px;
// // $headerSubLinkTransform1: none;

// // /*
// // 	Transparent Header
// // */
// // $transparentSubHeaderLinkColor1: $white;
// // $transparentSubHeaderNumberColor1: $white;
// // $transparentHeaderLinkColor1: $white;

// // /*
// // 	Special Color Header
// // */
// // $specialSubHeaderLinkColor1: blue;
// // $specialSubHeaderNumberColor1: blue;
// // $specialHeaderLinkColor1: blue;

// #header {
// 	position: relative;
// 	z-index: 9999;

// 	.sub-header {
// 		// background: $subHeaderBG1;
// 		@include clearfix;

// 		ul {
// 			// float: right;
// 			// margin: 0;
// 			// color: $subHeaderLinkColor1;
// 			// line-height: $subHeaderHeight1;
// 			// display: flex;
// 			// padding-left: 0;
// 			// background: var(--color-bg);

// 			li {
// 				// display: inline-block;

// 				&:first-child {
// 					// padding-right: 15px;
// 				}

// 				@media (max-width: 992px) {
// 					&:first-child {
// 						// padding-right: 10px;
// 					}
// 				}

// 				a {
// 					// color: $subHeaderLinkColor1;
// 					// font-size: $subHeaderLinkSize1;
// 				}

// 				&.language {
// 					// background-color: white;
// 				}
// 			}
// 		}

// 		.english-fake{
// 			display:none;
// 		}

// 		.switch:hover{
// 			cursor: pointer;
// 		}

// 		// .favorite {
// 		// 	padding: $subHeaderFavoritePadding1;
// 		// 	border-left: $subHeaderFavoriteBorderLeft1;

// 		// 	a {
// 		// 		font-size: $subHeaderFavoriteSize1;
// 		// 		font-weight: 700;
// 		// 		display: flex;
// 		// 		align-items: center;

// 		// 		i {
// 		// 			margin-right: 10px;
// 		// 			font-size: 20px;
// 		// 		}
// 		// 	}
// 		// }

// 		.number-ctn {
// 			// background: $subHeaderNumberBG1;
// 			// padding: $subHeaderNumberPadding1;
// 			// border-left: $subHeaderNumberBorderLeft1;
// 			// transition: $transition;

// 			&:hover {
// 				// background: $subHeaderNumberBGHover1;
// 				// color: $primaryButtonTextColorHover;
// 			}

// 			a {
// 				// color: $subHeaderNumberColor1;
// 				// font-size: $subHeaderNumberSize1;
// 				// font-weight: $subHeaderNumberWeight1;
// 				// opacity: $subHeaderNumberOpacity1;

// 				i {
// 					// margin-right: 10px;
// 					// font-size: var(--font-size-small);
// 					// border: $subHeaderNumberPhoneBorder1;
// 					// border-radius: $subHeaderNumberPhoneRadius1;
// 				}
// 			}
// 		}

// 		.share-ctn {
// 			// padding-left: 30px;
// 			// padding-right: 30px;
// 			// border-left: 1px solid #D8D8D8;

// 			// @media (max-width: 992px) {
// 			// 	display: none;
// 			// }

// 			ul {
// 				// margin: 0;
// 				// padding-left: 0;
// 				// display: flex;

// 				li {
// 					// margin-right: 15px;
// 					// padding-right: 0;

// 					&:last-child {
// 						// margin-right: 0;
// 					}
// 				}

// 				a {
// 					// display: inline-block;
// 					// font-size: 16px;
// 					// color: var(--color-text);
// 					// transition: $transition;

// 					&:hover {
// 						// color: #D8D8D8;
// 					}
// 				}
// 			}
// 		}
// 	}
// 	.main-header {
// 		// display: flex;
// 		// align-items: center;
// 		// justify-content: space-between;
// 		// height: $headerHeight1;
// 		// padding: 17px 40px;
// 		// background-color: $headerBG1;

// 		@media (max-width: 1095px) {
// 			// padding: 17px 20px;
// 		}

// 		.special-logo {
// 			// fill: $black;
// 			// width: 67px;
//       		// max-height: 55px;
// 		}

// 		.logo-ctn {
// 			// height: 66px;
// 			// display: flex;
// 			// align-items: center;

// 			> a {
// 				// height: 66px;
// 				// display: flex;
// 				// align-items: center;
// 			}

// 			img {
// 				// max-height: 100%;
// 			}

// 			.special-logo {
// 				// margin-left: 25px;
// 			}
// 		}

// 		.main-menu-ctn {
// 			// @media (max-width: 992px) {
// 			// 	display: none;
// 			// }

// 			.main-menu {
// 				// display: flex;
// 				// align-items: center;
// 				// justify-content: space-between;
// 				// padding-left: 0;
// 				// list-style-type: none;
// 				// position: relative;
// 				// z-index: 1;
// 				// margin: 0;
// 				// flex-flow: row wrap;
// 				// background: transparent;

// 				.item-menu {
// 					// position: relative;
// 					// display: inline-block;
// 					// padding-right: $headerLinkPaddingRight1;

// 					// @media (max-width: 1300px) {
// 					// 	padding-right: 25px;
// 					// }

// 					// @media (max-width: 1200px) {
// 					// 	padding-right: 15px;
// 					// }

// 					&:last-child {
// 						// padding-right: 0;
// 					}

// 					&.active-child{
// 						> a {
// 							// background: $headerSubMenuLinkBGHover1;
// 							// color: $headerSubMenuLinkColorHover1;
// 							// opacity: 0.5;
// 						}
// 					}

// 					.secondary-ul {
// 						// background: $headerSubMenuBG1;
// 						// margin: 0px;
// 						// width: 200px;
// 						// padding-left: 0;
// 						// padding-top: 15px;
// 						// padding-bottom: 15px;
// 						// left: 0;
// 						// position: absolute;
// 						// z-index: -1000;
// 						// opacity: 0;
// 						// display: none;

// 						a {
// 							// color: var(--color-text);
// 							// display: block;
// 							// font-size: $headerSubMenuLinkSize1;
// 							// color: $headerSubMenuLinkColor1;
// 							// font-weight: $headerLinkWeight1;
// 							// line-height: $headerSubMenuLinkLineHeight1;
// 							// text-transform: $headerSubLinkTransform1;
// 							// padding: 10px 30px 10px 30px;
// 						}

// 						&:before {
// 							// content: "";
// 							// width: 0;
// 							// height: 0;
// 							// border-left: 10px solid transparent;
// 							// border-right: 10px solid transparent;
// 							// border-bottom: 10px solid $headerSubMenuBG1;
// 							// position: absolute;
// 							// top: -10px;
// 							// left: 15px;
// 						}
// 					}

// 					> a {
// 						// font-size: $headerLinkSize1;
// 						// color: $headerLinkColor1;
// 						// font-weight: $headerLinkWeight1;
// 						// line-height: 66px;
// 						// text-transform: $headerLinkTransform1;
// 						// opacity: 1;
// 						// transition: $transition;

// 						// i {
// 						// 	margin-left: 10px;
// 						// 	font-size: 11px;
// 						// }

// 						// @media (max-width: 1200px) {
// 						// 	font-size: 13px;
// 						// }

// 						// @media (max-width: 1095px) {
// 						// 	font-size: 11px;
// 						// }

// 						// &.active {
// 						// 	opacity: 0.5;
// 						// }
// 					}

// 					&:hover {
// 						// cursor: pointer;

// 						> a {
// 							// opacity: 0.5;
// 						}

// 						.secondary-ul {
// 							// z-index: 200;
// 							// opacity: 1;
// 							// transition: z-index 0.5s step-start, opacity 0.5s ease;
// 							// display: block;

// 							li {
// 								// display: block;

// 								a {

// 									&.active, &:hover {
// 										// background: $headerSubMenuLinkBGHover1;
// 										// color: $headerSubMenuLinkColorHover1;
// 										// opacity: 0.5;
// 									}
// 								}
// 							}
// 						}
// 					}

// 				}
// 			}
// 		}
// 	}

	.header-menu-toggle {
		display: none;
		position: relative;
		right: 0;
		width: 30px;
		height: 25px;
		transition: all 0.5s ease;

		@media (max-width: 992px) {
			display: inline-block;
		}

		span {
			width: 100%;
			height: 3px;
			background-color: black;
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			-ms-filter: none;
			-webkit-filter: none;
			filter: none;
			opacity: 1;
			-webkit-transform-origin: center;
			-ms-transform-origin: center;
			transform-origin: center;
			transition: all .25s ease-in-out;

			&:nth-child(2){
				top: 10px;
			}

			&:nth-child(3){
				top: 20px;
			}
		}
	}

// 	// &.transparent {
// 	// 	.sub-header {
// 	// 		background: transparent;

// 	// 		ul {
// 	// 			li {
// 	// 				a {
// 	// 					color: $transparentSubHeaderLinkColor1;
// 	// 				}
// 	// 			}
// 	// 		}

// 	// 		.number-ctn {
// 	// 			a {
// 	// 				color: $transparentSubHeaderNumberColor1;
// 	// 			}
// 	// 		}
// 	// 	}
// 	// 	.main-header {
// 	// 		background: transparent;

// 	// 		.main-menu-ctn {
// 	// 			.main-menu {
// 	// 				.item-menu {
// 	// 					> a {
// 	// 						color: $transparentHeaderLinkColor1;
// 	// 					}
// 	// 				}
// 	// 			}
// 	// 		}
// 	// 	}
// 	// }

// 	// &.special {
// 	// 	.sub-header {
// 	// 		background: transparent;

// 	// 		ul {
// 	// 			li {
// 	// 				a {
// 	// 					color: $specialSubHeaderLinkColor1;
// 	// 				}
// 	// 			}
// 	// 		}

// 	// 		.number-ctn {
// 	// 			a {
// 	// 				color: $specialSubHeaderNumberColor1;
// 	// 			}
// 	// 		}
// 	// 	}
// 	// 	.main-header {
// 	// 		background: transparent;

// 	// 		.main-menu-ctn {
// 	// 			.main-menu {
// 	// 				.item-menu {
// 	// 					> a {
// 	// 						color: $specialHeaderLinkColor1;
// 	// 					}
// 	// 				}
// 	// 			}
// 	// 		}
// 	// 	}
// 	// }
// }

// //Opened Menu
// body {
// 	// &.-open-menu {
// 	// 	#header {
// 	// 		.header-menu-toggle {
// 	// 			height: 25px;

// 	// 			span {
// 	// 				&:first-child {
// 	// 					transform: rotate(45deg);
// 	// 					top: 12px;
// 	// 				}

// 	// 				&:nth-child(2) {
// 	// 					opacity: 0;
// 	// 				}

// 	// 				&:nth-child(3) {
// 	// 					transform: rotate(-45deg);
// 	// 					top: 12px;
// 	// 				}
// 	// 			}
// 	// 		}
// 	// 	}
// 	// }
// }

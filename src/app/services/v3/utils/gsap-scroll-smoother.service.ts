import { Injectable } from '@angular/core';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ScrollSmoother } from 'gsap/ScrollSmoother';

@Injectable({
  providedIn: 'root'
})
export class GsapScrollSmootherService {
  private scrollSmoother: any = null;
  private isInitialized = false;

  constructor() {
    // Enregistrer les plugins GSAP
    gsap.registerPlugin(ScrollTrigger, ScrollSmoother);
  }

  /**
   * Initialise ScrollSmoother avec les options par défaut
   */
  init(options?: any): void {
    if (this.isInitialized) {
      console.warn('ScrollSmoother is already initialized');
      return;
    }

    // Options par défaut pour ScrollSmoother
    const defaultOptions = {
      wrapper: '#smooth-wrapper',
      content: '#smooth-content',
      smooth: 1.5,
      effects: true,
      smoothTouch: 0.1,
      normalizeScroll: true,
      ignoreMobileResize: true,
      ...options
    };

    try {
      this.scrollSmoother = ScrollSmoother.create(defaultOptions);
      this.isInitialized = true;
      console.log('ScrollSmoother initialized successfully');
    } catch (error) {
      console.error('Error initializing ScrollSmoother:', error);
    }
  }

  /**
   * Détruit l'instance de ScrollSmoother
   */
  destroy(): void {
    if (this.scrollSmoother) {
      this.scrollSmoother.kill();
      this.scrollSmoother = null;
      this.isInitialized = false;
      console.log('ScrollSmoother destroyed');
    }
  }

  /**
   * Scroll vers un élément ou une position
   */
  scrollTo(target: string | number | Element, options?: any): void {
    if (this.scrollSmoother) {
      this.scrollSmoother.scrollTo(target, options);
    }
  }

  /**
   * Rafraîchit ScrollSmoother
   */
  refresh(): void {
    if (this.scrollSmoother) {
      this.scrollSmoother.refresh();
    }
  }

  /**
   * Pause/reprend le smooth scroll
   */
  paused(value?: boolean): boolean | void {
    if (this.scrollSmoother) {
      if (value !== undefined) {
        this.scrollSmoother.paused(value);
      } else {
        return this.scrollSmoother.paused();
      }
    }
    return false;
  }

  /**
   * Obtient la position de scroll actuelle
   */
  scrollTop(): number {
    return this.scrollSmoother ? this.scrollSmoother.scrollTop() : 0;
  }

  /**
   * Vérifie si ScrollSmoother est initialisé
   */
  isReady(): boolean {
    return this.isInitialized && this.scrollSmoother !== null;
  }

  /**
   * Obtient l'instance ScrollSmoother pour des manipulations avancées
   */
  getInstance(): any {
    return this.scrollSmoother;
  }

  /**
   * Met à jour la vitesse du smooth scroll
   */
  updateSpeed(speed: number): void {
    if (this.scrollSmoother) {
      this.scrollSmoother.smooth(speed);
    }
  }

  /**
   * Active/désactive les effets
   */
  toggleEffects(enabled: boolean): void {
    if (this.scrollSmoother) {
      this.scrollSmoother.effects(enabled);
    }
  }
}

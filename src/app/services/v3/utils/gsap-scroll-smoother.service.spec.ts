import { TestBed } from '@angular/core/testing';
import { GsapScrollSmootherService } from './gsap-scroll-smoother.service';

describe('GsapScrollSmootherService', () => {
  let service: GsapScrollSmootherService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(GsapScrollSmootherService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should not be initialized by default', () => {
    expect(service.isReady()).toBeFalsy();
  });

  it('should have scrollTop method', () => {
    expect(service.scrollTop()).toBe(0);
  });

  it('should have paused method that returns false when not initialized', () => {
    expect(service.paused()).toBeFalsy();
  });
});

.scroll-demo {
  .demo-section {
    min-height: 100vh;
    padding: 2rem;
    position: relative;
    
    &.hero-section {
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      overflow: hidden;
      
      .hero-bg {
        position: absolute;
        top: -20%;
        left: -20%;
        width: 140%;
        height: 140%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        background-size: 50px 50px;
      }
      
      .hero-content {
        text-align: center;
        z-index: 2;
        
        h1 {
          font-size: 3rem;
          margin-bottom: 1rem;
          font-weight: bold;
        }
        
        p {
          font-size: 1.2rem;
          margin-bottom: 2rem;
          opacity: 0.9;
        }
      }
    }
    
    &:nth-child(even) {
      background-color: #f8f9fa;
    }
    
    &:nth-child(odd) {
      background-color: #ffffff;
    }
  }
  
  .content-wrapper {
    max-width: 800px;
    margin: 0 auto;
    
    h2 {
      font-size: 2.5rem;
      margin-bottom: 2rem;
      text-align: center;
      color: #333;
    }
  }
  
  .parallax-element {
    margin: 2rem 0;
    padding: 1.5rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    border-left: 4px solid #667eea;
    
    p {
      margin: 0;
      font-size: 1.1rem;
      color: #555;
    }
  }
  
  .demo-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .btn-demo, .btn-speed, .btn-top {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  }
  
  .controls-section {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    color: white;
    
    .speed-controls {
      text-align: center;
      margin-bottom: 2rem;
      
      label {
        display: block;
        margin-bottom: 1rem;
        font-size: 1.1rem;
      }
      
      .btn-speed {
        margin: 0 0.5rem;
        background: rgba(255, 255, 255, 0.2);
        
        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
    
    .btn-top {
      display: block;
      margin: 0 auto;
      background: #28a745;
      
      &:hover {
        background: #218838;
      }
    }
  }
  
  .long-content {
    .text-blocks {
      display: grid;
      gap: 2rem;
    }
    
    .text-block {
      padding: 1.5rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      
      h3 {
        color: #667eea;
        margin-bottom: 1rem;
      }
      
      p {
        line-height: 1.6;
        color: #666;
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .scroll-demo {
    .demo-section {
      padding: 1rem;
      
      &.hero-section {
        .hero-content {
          h1 {
            font-size: 2rem;
          }
          
          p {
            font-size: 1rem;
          }
        }
      }
    }
    
    .content-wrapper {
      h2 {
        font-size: 2rem;
      }
    }
    
    .demo-controls {
      flex-direction: column;
      align-items: center;
    }
    
    .btn-demo, .btn-speed, .btn-top {
      width: 200px;
    }
  }
}

<!-- Composant de démonstration pour ScrollSmoother -->
<div class="scroll-demo">
  
  <!-- Section Hero avec parallaxe -->
  <section id="hero" class="demo-section hero-section" data-speed="0.8">
    <div class="hero-bg" data-speed="0.5"></div>
    <div class="hero-content">
      <h1 data-speed="1.2">GSAP ScrollSmoother</h1>
      <p data-speed="1.1">Défilement fluide et effets de parallaxe</p>
      <div class="demo-controls">
        <button (click)="scrollToSection('section1')" class="btn-demo">Section 1</button>
        <button (click)="scrollToSection('section2')" class="btn-demo">Section 2</button>
        <button (click)="toggleSmoothing()" class="btn-demo">Toggle Smooth</button>
      </div>
    </div>
  </section>

  <!-- Section 1 avec effets -->
  <section id="section1" class="demo-section" data-speed="1.0">
    <div class="content-wrapper">
      <h2 data-speed="1.3">Section avec parallaxe</h2>
      <div class="parallax-element" data-speed="0.6">
        <p>Cet élément défile plus lentement (data-speed="0.6")</p>
      </div>
      <div class="parallax-element" data-speed="1.4">
        <p>Cet élément défile plus rapidement (data-speed="1.4")</p>
      </div>
      <div class="parallax-element" data-speed="-0.3">
        <p>Cet élément défile en sens inverse (data-speed="-0.3")</p>
      </div>
    </div>
  </section>

  <!-- Section 2 avec contrôles -->
  <section id="section2" class="demo-section controls-section">
    <div class="content-wrapper">
      <h2>Contrôles ScrollSmoother</h2>
      <div class="speed-controls">
        <label>Vitesse du smooth scroll:</label>
        <button (click)="updateSpeed(0.5)" class="btn-speed">0.5x</button>
        <button (click)="updateSpeed(1.0)" class="btn-speed">1.0x</button>
        <button (click)="updateSpeed(1.5)" class="btn-speed">1.5x</button>
        <button (click)="updateSpeed(2.0)" class="btn-speed">2.0x</button>
      </div>
      <button (click)="scrollToTop()" class="btn-top">Retour en haut</button>
    </div>
  </section>

  <!-- Section de contenu long pour tester le scroll -->
  <section class="demo-section long-content">
    <div class="content-wrapper">
      <h2>Contenu de test</h2>
      <div class="text-blocks">
        <div class="text-block" data-speed="0.9" *ngFor="let i of [1,2,3,4,5,6,7,8,9,10]">
          <h3>Bloc de texte {{ i }}</h3>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        </div>
      </div>
    </div>
  </section>

</div>

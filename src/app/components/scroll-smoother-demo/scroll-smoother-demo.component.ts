import { Component, OnInit, AfterViewInit } from '@angular/core';
import { GsapScrollSmootherService } from '@/services/v3/utils/gsap-scroll-smoother.service';

@Component({
  selector: 'app-scroll-smoother-demo',
  templateUrl: './scroll-smoother-demo.component.html',
  styleUrls: ['./scroll-smoother-demo.component.scss']
})
export class ScrollSmootherDemoComponent implements OnInit, AfterViewInit {

  constructor(private scrollSmoother: GsapScrollSmootherService) { }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    // Attendre que ScrollSmoother soit initialisé
    setTimeout(() => {
      this.setupScrollEffects();
    }, 500);
  }

  private setupScrollEffects(): void {
    if (this.scrollSmoother.isReady()) {
      console.log('ScrollSmoother est prêt pour les effets personnalisés');
      
      // Ici vous pouvez ajouter des animations ScrollTrigger personnalisées
      // Exemple : animations d'apparition au scroll
    }
  }

  scrollToTop(): void {
    this.scrollSmoother.scrollTo(0, { duration: 1.5 });
  }

  scrollToSection(sectionId: string): void {
    this.scrollSmoother.scrollTo(`#${sectionId}`, { duration: 2 });
  }

  toggleSmoothing(): void {
    const isPaused = this.scrollSmoother.paused();
    this.scrollSmoother.paused(!isPaused);
    console.log(`ScrollSmoother ${isPaused ? 'activé' : 'désactivé'}`);
  }

  updateSpeed(speed: number): void {
    this.scrollSmoother.updateSpeed(speed);
    console.log(`Vitesse mise à jour: ${speed}`);
  }
}

import { Component, OnInit, OnDestroy, AfterViewInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { GridHelperService } from './services/v3/utils/GridHelper.service';
import { GsapScrollSmootherService } from './services/v3/utils/gsap-scroll-smoother.service';
import { environment } from '@/../environments/environment';

declare var Cookiebot: any;

@Component({
  selector: 'e-closion',
  templateUrl: './app.component.html'
})

export class AppComponent implements OnInit, AfterViewInit, OnDestroy {
  title = 'app';
  html;

  constructor (
    public translate: TranslateService,
    private router: Router,
    private gridHelper: GridHelperService,
    private scrollSmootherService: GsapScrollSmootherService
  ) {
    this.html = document.querySelector('html');

    const defaultLang = 'fr';
    const urlLang = window.location.pathname.split('/')[1];
    const userLang = ['en', 'fr'].includes(urlLang) ? urlLang : defaultLang;

    translate.setDefaultLang(defaultLang);
    translate.use(userLang);

    router.events.subscribe(event => {
      // route change event
      if (event instanceof NavigationEnd) {
        // Utiliser ScrollSmoother pour le scroll vers le haut si disponible
        if (event.url.indexOf('#') === -1) {
          if (this.scrollSmootherService.isReady()) {
            this.scrollSmootherService.scrollTo(0);
          } else {
            window.scrollTo(0, 0);
          }
        }
        document.querySelector('body').classList.remove('-open-menu');
        this.html.classList.remove('-no-scroll');

        // #############################################
        // ####### A CHANGER AVANT MISE EN LIGNE #######
        // #############################################

        // Google analytics -> force send new page
        // GA 4
        if ((<any>window).gtag) {
          (<any>window).gtag('set', 'page', event.urlAfterRedirects);
          (<any>window).gtag('send', 'pageview');
        }
      }
    });
  }

  ngOnInit() {
		this.cookiesConsentEvents();

    if (!environment.production) {
      this.gridHelper.initialize();
    }
	}

  ngAfterViewInit() {
    // Initialiser ScrollSmoother après que la vue soit complètement chargée
    setTimeout(() => {
      this.initScrollSmoother();
    }, 100);
  }

  ngOnDestroy() {
    // Nettoyer ScrollSmoother lors de la destruction du composant
    this.scrollSmootherService.destroy();
  }

  private initScrollSmoother() {
    // Configuration personnalisée pour ScrollSmoother
    const scrollSmootherOptions = {
      wrapper: '#smooth-wrapper',
      content: '#smooth-content',
      smooth: 1.2, // Vitesse du smooth scroll (1 = normal, 2 = plus lent)
      effects: true, // Active les effets de parallaxe
      smoothTouch: 0.1, // Smooth scroll sur mobile (0.1 = léger)
      normalizeScroll: true, // Normalise le scroll entre différents navigateurs
      ignoreMobileResize: true // Ignore le redimensionnement mobile
    };

    this.scrollSmootherService.init(scrollSmootherOptions);
  }
	
	/**
	 * Set up event listeners for Cookiebot's "accept" and "decline" events.
	 * When users accept or decline cookies, reloads the page.
	 */
	cookiesConsentEvents() {
		if (Cookiebot) {

			window.addEventListener('CookiebotOnAccept', () => {
				if (Cookiebot.changed === true) {
					location.reload();
				}
			}, false);

			window.addEventListener('CookiebotOnDecline', () => {
				if (Cookiebot.changed === true) {
					location.reload();
				}
			}, false);
		}
  	}
}
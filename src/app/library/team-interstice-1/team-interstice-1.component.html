<div class="team-interstice-1-cpn container">
	<h3 class="-emphase-title">{{ 'library.team-interstice-1.title' | translate }}</h3>

	<ul id="og-grid" class="og-grid">
		<ng-container *ngFor="let teamMember of teamMembers">
			<li class="item-wrap"  *ngIf="teamMember.teammembers_category == 'broker'">
				<lib-team-card-2 [teamMember]="teamMember"></lib-team-card-2>
			</li>
		</ng-container>
	</ul>

	<h3 class="-emphase-title">{{ 'library.team-interstice-1.title-admin' | translate }}</h3>

	<ul id="og-grid" class="og-grid">
		<ng-container *ngFor="let teamMember of teamMembers">
			<li class="item-wrap"  *ngIf="teamMember.teammembers_category != 'broker'">
				<lib-team-card-2 [teamMember]="teamMember"></lib-team-card-2>
			</li>
		</ng-container>
	</ul>
	

	<div class="team-info-box">
		<div class="team-info-box-content">
			<p class="title">{{ 'library.team-interstice-1.infobox.title' | translate }}</p>
			<p class="description">{{ 'library.team-interstice-1.infobox.description' | translate }}</p>
			<div class="btn-ctn">
				<a [routerLink]="['urls.contact' | translate ]" class="main-button -primary">{{ 'library.team-interstice-1.infobox.button' | translate }}</a>
			</div>
		</div>
	</div>
</div>

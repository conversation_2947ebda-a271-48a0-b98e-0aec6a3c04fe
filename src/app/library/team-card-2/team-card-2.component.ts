import { Component, Input, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'lib-team-card-2',
  templateUrl: './team-card-2.component.html'
})

export class TeamCard2Component implements OnInit {
  @Input() teamMember;

  cleanVideo;

  constructor (
    private sanitizer: DomSanitizer
  ) { }

  ngOnInit () {
    // Ensure the video URL is sanitized to prevent security issues
    if (this.teamMember.video_formatted) {
      this.cleanVideo = this.sanitizer.bypassSecurityTrustResourceUrl(this.teamMember.video_formatted);
    }
  }

  onOpenPreview ($event) {
    $event.preventDefault();
    const currentTarget = $event.currentTarget;
    const currentLi = currentTarget.parentNode.parentNode;

    setTimeout(function () {
      currentLi.querySelectorAll('.og-expander .og-expander-inner')[0].scrollIntoView({ behavior: 'smooth' });
    }, 100);

    if (currentLi.classList.contains('og-expanded')) {
      return;
    }

    const allLi: HTMLCollection = document.getElementsByClassName('item-wrap');

    for (let i = 0, len = allLi.length; i < len; i++) {
      const li = (allLi[i] as HTMLElement);
      if (li.classList.contains('og-expanded')) {
        const formerLiHeight = (li.querySelectorAll('.item')[0] as HTMLElement).offsetHeight;
        const currentExpend = li.querySelectorAll('.og-expander')[0] as HTMLElement;

        currentExpend.style.height = '0px';
        li.style.height = formerLiHeight + 'px';
        li.classList.remove('og-expanded');
      }
    }

    const currentLiHeight = currentLi.querySelectorAll('.item')[0].offsetHeight;
    const currentExpend = currentLi.querySelectorAll('.og-expander')[0];
    const innerExpendHeight = this.outerHeight(currentExpend.querySelectorAll('.og-expander-inner')[0]);

    currentLi.classList.add('og-expanded');
    currentExpend.style.height = innerExpendHeight + 'px';
    currentLi.style.height = currentLiHeight + innerExpendHeight + 20 + 'px';
  }

  onClosePreview ($event) {
    $event.preventDefault();
    const currentTarget = $event.currentTarget;
    const currentLi = currentTarget.parentNode.parentNode.parentNode.parentNode;
    const formerLiHeight = currentLi.querySelectorAll('.item')[0].offsetHeight;
    const currentExpend = currentLi.querySelectorAll('.og-expander')[0];

    currentExpend.style.height = '0px';
    currentLi.style.height = formerLiHeight + 'px';
    currentLi.classList.remove('og-expanded');

    setTimeout(function () {
      currentLi.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  }

  outerHeight (el) {
    let height = el.offsetHeight;
    const style = getComputedStyle(el);

    height += parseInt(style.marginTop) + parseInt(style.marginBottom);
    return height;
  }
}

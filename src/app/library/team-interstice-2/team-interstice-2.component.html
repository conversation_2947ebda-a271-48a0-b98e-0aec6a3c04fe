<div class="team-interstice-2-cpn container">
	<ul id="og-grid" class="og-grid">
    <li class="item-wrap" *ngFor="let teamMember of teamMembers">
      <lib-team-card-2 [teamMember]="teamMember"></lib-team-card-2>
    </li>

		<li class="item-wrap">
      <div class="team-info-box">
        <div class="team-info-box-content">
          <p class="title">{{ 'library.team-interstice-2.infobox.title' | translate }}</p>
          <a [routerLink]="['urls.contact' | translate ]" class="main-button -primary">{{ 'library.team-interstice-2.infobox.button' | translate }}</a>
        </div>
      </div>
		</li>
	</ul>
</div>

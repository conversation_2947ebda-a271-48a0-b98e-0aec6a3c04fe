# Guide d'utilisation de GSAP ScrollSmoother

## Vue d'ensemble

GSAP ScrollSmoother a été intégré dans ce projet Angular pour fournir un défilement fluide et des effets de parallaxe. Cette intégration utilise GSAP 3.x avec les plugins ScrollTrigger et ScrollSmoother.

## Architecture

### Service Angular
- **Fichier**: `src/app/services/v3/utils/gsap-scroll-smoother.service.ts`
- **Responsabilité**: Encapsule toute la logique de ScrollSmoother
- **Injection**: Disponible dans toute l'application via `providedIn: 'root'`

### Structure HTML
Le template principal (`app.component.html`) a été modifié pour inclure la structure requise :
```html
<div id="smooth-wrapper">
  <div id="smooth-content">
    <router-outlet></router-outlet>
  </div>
</div>
```

### Initialisation
ScrollSmoother est initialisé dans `AppComponent.ngAfterViewInit()` avec les options suivantes :
- **smooth**: 1.2 (vitesse du smooth scroll)
- **effects**: true (active les effets de parallaxe)
- **smoothTouch**: 0.1 (smooth scroll léger sur mobile)
- **normalizeScroll**: true (normalise entre navigateurs)

## Utilisation

### Effets de parallaxe
Ajoutez l'attribut `data-speed` à vos éléments HTML :

```html
<!-- Défilement plus lent (effet de profondeur) -->
<div data-speed="0.5">Cet élément défile plus lentement</div>

<!-- Défilement plus rapide -->
<div data-speed="1.5">Cet élément défile plus rapidement</div>

<!-- Défilement en sens inverse -->
<div data-speed="-0.5">Cet élément défile vers l'arrière</div>
```

### Utilisation du service dans un composant

```typescript
import { Component, OnInit } from '@angular/core';
import { GsapScrollSmootherService } from '@/services/v3/utils/gsap-scroll-smoother.service';

@Component({
  selector: 'app-example',
  templateUrl: './example.component.html'
})
export class ExampleComponent implements OnInit {
  
  constructor(private scrollSmoother: GsapScrollSmootherService) {}

  ngOnInit() {
    // Vérifier si ScrollSmoother est prêt
    if (this.scrollSmoother.isReady()) {
      console.log('ScrollSmoother est actif');
    }
  }

  scrollToTop() {
    // Scroll fluide vers le haut
    this.scrollSmoother.scrollTo(0);
  }

  scrollToElement() {
    // Scroll vers un élément spécifique
    this.scrollSmoother.scrollTo('#mon-element', { duration: 2 });
  }

  pauseScrolling() {
    // Mettre en pause le smooth scroll
    this.scrollSmoother.paused(true);
  }

  resumeScrolling() {
    // Reprendre le smooth scroll
    this.scrollSmoother.paused(false);
  }
}
```

### Méthodes disponibles du service

- `init(options?)`: Initialise ScrollSmoother
- `destroy()`: Détruit l'instance
- `scrollTo(target, options?)`: Scroll vers une cible
- `refresh()`: Rafraîchit ScrollSmoother
- `paused(value?)`: Pause/reprend ou obtient l'état
- `scrollTop()`: Obtient la position de scroll actuelle
- `isReady()`: Vérifie si ScrollSmoother est initialisé
- `getInstance()`: Obtient l'instance GSAP pour usage avancé
- `updateSpeed(speed)`: Met à jour la vitesse du smooth scroll
- `toggleEffects(enabled)`: Active/désactive les effets

## Styles CSS

Des styles optimisés ont été ajoutés dans `src/styles.scss` :

```scss
// Structure de base
#smooth-wrapper {
  overflow: hidden;
}

#smooth-content {
  overflow: visible;
  width: 100%;
}

// Optimisations de performance
.smooth-scroll-optimized {
  will-change: transform;
  transform: translateZ(0);
}

// Désactiver le smooth scroll sur certains éléments
.no-smooth {
  will-change: auto;
  transform: none;
}
```

## Intégration avec le routing Angular

Le service est intégré avec le système de routing d'Angular. Lors des changements de route, ScrollSmoother est utilisé pour le scroll vers le haut au lieu de `window.scrollTo()`.

## Considérations de performance

1. **Mobile**: Le smooth scroll est réduit sur mobile (`smoothTouch: 0.1`)
2. **Optimisations CSS**: Utilisez `will-change: transform` sur les éléments animés
3. **Désactivation sélective**: Utilisez la classe `.no-smooth` pour exclure certains éléments

## Dépannage

### ScrollSmoother ne s'initialise pas
- Vérifiez que les éléments `#smooth-wrapper` et `#smooth-content` existent
- Assurez-vous que GSAP 3.x est installé
- Vérifiez la console pour les erreurs

### Conflits avec d'autres scripts de scroll
- Désactivez les autres bibliothèques de smooth scroll
- Utilisez `this.scrollSmoother.paused(true)` temporairement si nécessaire

### Performance dégradée
- Réduisez la valeur `smooth` (ex: 1.0 au lieu de 1.2)
- Limitez le nombre d'éléments avec `data-speed`
- Utilisez `effects: false` pour désactiver les effets de parallaxe

## Exemples d'effets avancés

### Parallaxe d'arrière-plan
```html
<section class="hero" data-speed="0.5">
  <div class="hero-bg" data-speed="0.3"></div>
  <div class="hero-content">Contenu principal</div>
</section>
```

### Animation au scroll
```typescript
// Dans votre composant
ngAfterViewInit() {
  if (this.scrollSmoother.isReady()) {
    // Créer des animations personnalisées avec ScrollTrigger
    gsap.to('.fade-in', {
      opacity: 1,
      y: 0,
      scrollTrigger: {
        trigger: '.fade-in',
        start: 'top 80%',
        end: 'bottom 20%',
        scrub: true
      }
    });
  }
}
```

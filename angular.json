{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"e-closion-v3-frontend": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"aot": true, "outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets", "src/favicon.ico", "src/service-worker.js"], "styles": ["node_modules/nouislider/dist/nouislider.min.css", "node_modules/swiper/swiper-bundle.min.css", "node_modules/mapbox-gl/dist/mapbox-gl.css", "node_modules/@angular/cdk/overlay-prebuilt.css", "src/styles.scss"], "scripts": ["node_modules/nouislider/dist/nouislider.min.js", "node_modules/numeral/min/numeral.min.js", "node_modules/chart.js/dist/chart.umd.js"], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true, "allowedCommonJsDependencies": ["places.js", "chart.js", "<PERSON>ui<PERSON><PERSON><PERSON>", "numeral", "mapbox-gl", "moment"]}, "configurations": {"production": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "e-closion-v3-frontend:build"}, "configurations": {"production": {"browserTarget": "e-closion-v3-frontend:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "e-closion-v3-frontend:build"}}}}}, "schematics": {"@schematics/angular:component": {"prefix": "app", "style": "scss"}, "@schematics/angular:directive": {"prefix": "app"}}}